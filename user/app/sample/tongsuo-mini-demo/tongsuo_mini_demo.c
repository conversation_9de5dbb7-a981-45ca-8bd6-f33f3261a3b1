/*
 * Copyright (C) 2024 The Tongsuo Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <err.h>
#include <uapi/err.h>
#include <stdio.h>

#include <tongsuo/minisuo.h>
#include <tongsuo/sm3.h>
#include <tongsuo/sm4.h>
#include <tongsuo/ascon.h>

#define LOG_TAG "tongsuo-mini-demo"

static void demo_sm3_hash(void)
{
    void *ctx;
    unsigned char hash[TSM_SM3_DIGEST_LEN];
    const char *message = "Hello Tongsuo Mini in TEE!";
    int ret;

    printf("=== SM3 Hash Demo ===\n");
    
    // Using oneshot API for simplicity
    ret = tsm_sm3_oneshot((const unsigned char *)message, strlen(message), hash);
    if (ret != TSM_OK) {
        printf("SM3 oneshot failed: %d\n", ret);
        return;
    }

    printf("Message: %s\n", message);
    printf("SM3 Hash: ");
    for (int i = 0; i < TSM_SM3_DIGEST_LEN; i++) {
        printf("%02x", hash[i]);
    }
    printf("\n");
}

static void demo_sm4_encrypt(void)
{
    unsigned char plaintext[16] = "Hello SM4 TEE!!!";
    unsigned char ciphertext[32]; // Extra space for potential padding
    unsigned char decrypted[32];
    unsigned char key_data[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef,
        0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    unsigned char iv[16] = {0}; // Zero IV for simplicity
    size_t ct_len, dt_len;
    int ret;

    printf("=== SM4 Encryption Demo ===\n");

    // Encrypt using oneshot API
    ret = tsm_sm4_oneshot(TSM_CIPH_MODE_ECB, key_data, iv,
                          plaintext, 16, ciphertext, &ct_len,
                          TSM_CIPH_FLAG_ENCRYPT | TSM_CIPH_FLAG_NO_PAD);
    if (ret != TSM_OK) {
        printf("SM4 encrypt failed: %d\n", ret);
        return;
    }

    // Decrypt using oneshot API
    ret = tsm_sm4_oneshot(TSM_CIPH_MODE_ECB, key_data, iv,
                          ciphertext, ct_len, decrypted, &dt_len,
                          TSM_CIPH_FLAG_DECRYPT | TSM_CIPH_FLAG_NO_PAD);
    if (ret != TSM_OK) {
        printf("SM4 decrypt failed: %d\n", ret);
        return;
    }

    printf("Plaintext:  ");
    for (int i = 0; i < 16; i++) {
        printf("%02x", plaintext[i]);
    }
    printf("\n");

    printf("Ciphertext: ");
    for (size_t i = 0; i < ct_len; i++) {
        printf("%02x", ciphertext[i]);
    }
    printf("\n");

    printf("Decrypted:  ");
    for (size_t i = 0; i < dt_len; i++) {
        printf("%02x", decrypted[i]);
    }
    printf("\n");

    if (dt_len == 16 && memcmp(plaintext, decrypted, 16) == 0) {
        printf("SM4 encrypt/decrypt test PASSED\n");
    } else {
        printf("SM4 encrypt/decrypt test FAILED\n");
    }
}

static void demo_ascon_aead(void)
{
    unsigned char key[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };
    unsigned char nonce[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };
    const char *plaintext = "ASCON in TEE!";
    size_t pt_len = strlen(plaintext);
    unsigned char ciphertext[64];  // Space for ciphertext + tag
    size_t ct_len;
    int ret;

    printf("=== ASCON AEAD Demo ===\n");

    // Encrypt using oneshot API - tag is appended to ciphertext
    ret = tsm_ascon_aead_oneshot(TSM_ASCON_AEAD_128, key, nonce,
                                 NULL, 0,  // no additional data
                                 (const unsigned char *)plaintext, pt_len,
                                 ciphertext, &ct_len,
                                 TSM_CIPH_FLAG_ENCRYPT);
    if (ret != TSM_OK) {
        printf("ASCON AEAD encrypt failed: %d\n", ret);
        return;
    }

    printf("Plaintext: %s\n", plaintext);
    printf("Ciphertext+Tag (%zu bytes): ", ct_len);
    for (size_t i = 0; i < ct_len; i++) {
        printf("%02x", ciphertext[i]);
    }
    printf("\n");

    // Show separate ciphertext and tag
    if (ct_len > TSM_ASCON_AEAD_TAG_LEN) {
        size_t cipher_len = ct_len - TSM_ASCON_AEAD_TAG_LEN;
        printf("Ciphertext: ");
        for (size_t i = 0; i < cipher_len; i++) {
            printf("%02x", ciphertext[i]);
        }
        printf("\n");

        printf("Tag: ");
        for (size_t i = cipher_len; i < ct_len; i++) {
            printf("%02x", ciphertext[i]);
        }
        printf("\n");
    }
}

int main(void)
{
    printf("Starting Tongsuo Mini Demo in TEE\n");

    // Demonstrate SM3 hash
    demo_sm3_hash();

    // Demonstrate SM4 encryption
    demo_sm4_encrypt();

    // Demonstrate ASCON AEAD
    demo_ascon_aead();

    printf("Tongsuo Mini Demo completed successfully\n");

    return 0;
} 