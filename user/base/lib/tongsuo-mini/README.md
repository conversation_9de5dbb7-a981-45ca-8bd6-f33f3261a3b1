# Tongsuo-mini for Trusty TEE

本库是 [tongsuo-mini](https://github.com/Tongsuo-Project/tongsuo-mini) 在 Trusty TEE 环境中的集成封装。

## 简介

Tongsuo-mini（铜锁迷你版）是一个轻量级的密码学算法库，专为嵌入式系统和物联网设备等资源受限场景设计。在 TEE（可信执行环境）中，它提供了以下核心算法：

- **SM3**: 国密哈希算法
- **SM4**: 国密分组密码算法  
- **ASCON**: 轻量级 AEAD 认证加密算法
- **HMAC**: 基于哈希的消息认证码

## 构建配置

默认启用的算法模块：
- `TONGSUO_MINI_WITH_SM3=true` - SM3 哈希算法
- `TONGSUO_MINI_WITH_SM4=true` - SM4 分组密码
- `TONGSUO_MINI_WITH_ASCON=true` - ASCON AEAD
- `TONGSUO_MINI_WITH_HMAC=true` - HMAC 消息认证

可选模块（默认关闭）：
- `TONGSUO_MINI_WITH_ASN1=false` - ASN.1 支持
- `TONGSUO_MINI_WITH_OSCORE=false` - OSCORE 协议支持

## 使用方法

### 1. 在应用中添加依赖

在您的应用的 `rules.mk` 文件中添加依赖：

```makefile
MODULE_DEPS += \
    user/base/lib/tongsuo-mini \
    user/base/lib/libc-rctee
```

### 2. 包含头文件

```c
#include <tongsuo/sm3.h>
#include <tongsuo/sm4.h>
#include <tongsuo/ascon.h>
#include <tongsuo/hmac.h>
```

### 3. 使用示例

#### SM3 哈希计算
```c
#include <tongsuo/sm3.h>

void compute_sm3_hash(void) {
    tsm_sm3_ctx_t ctx;
    unsigned char hash[TSM_SM3_DIGEST_LENGTH];
    const char *message = "Hello World";
    
    tsm_sm3_init(&ctx);
    tsm_sm3_update(&ctx, (const unsigned char *)message, strlen(message));
    tsm_sm3_final(&ctx, hash);
    
    // hash 数组现在包含 SM3 哈希值
}
```

#### SM4 加解密
```c
#include <tongsuo/sm4.h>

void encrypt_with_sm4(void) {
    tsm_sm4_key_t key;
    unsigned char key_data[16] = {/* 16 字节密钥 */};
    unsigned char plaintext[16] = {/* 16 字节明文 */};
    unsigned char ciphertext[16];
    
    tsm_sm4_set_key(&key, key_data);
    tsm_sm4_encrypt(&key, plaintext, ciphertext);
    
    // ciphertext 现在包含加密后的数据
}
```

#### ASCON AEAD 加密
```c
#include <tongsuo/ascon.h>

void encrypt_with_ascon(void) {
    tsm_ascon_aead_ctx_t ctx;
    unsigned char key[16] = {/* 密钥 */};
    unsigned char nonce[16] = {/* 随机数 */};
    unsigned char plaintext[] = "Secret message";
    unsigned char ciphertext[64];
    unsigned char tag[16];
    size_t ct_len, tag_len;
    
    tsm_ascon_aead_encrypt(&ctx, key, 16, nonce, 16,
                           NULL, 0,  // 无额外认证数据
                           plaintext, strlen((char*)plaintext),
                           ciphertext, &ct_len, tag, &tag_len);
                           
    // ciphertext 和 tag 包含加密结果和认证标签
}
```

## 示例应用

参考 `user/app/sample/tongsuo-mini-demo/` 目录下的完整示例应用，展示了如何在 TEE 环境中使用 tongsuo-mini 的各种算法。

## 性能特点

- **内存占用小**: 专为资源受限环境设计
- **模块化构建**: 只编译需要的算法模块
- **TEE 优化**: 适配 Trusty TEE 环境的内存管理和安全要求
- **国密支持**: 原生支持中国商用密码标准

## 扩展算法

未来可以考虑扩展以下算法：
- **SM2**: 国密椭圆曲线公钥密码算法
- **EdDSA**: Edwards 曲线数字签名算法
- **AES**: 高级加密标准
- **SHA**: 安全哈希算法系列

## 许可证

本库遵循 Apache 2.0 许可证。详见上游项目 [tongsuo-mini](https://github.com/Tongsuo-Project/tongsuo-mini) 的许可证条款。 