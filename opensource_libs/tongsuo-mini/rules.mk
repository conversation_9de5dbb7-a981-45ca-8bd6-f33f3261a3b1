# Copyright (C) 2023 The Tongsuo Project Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file is used for integrating tongsuo-mini into Trusty TEE build system

LOCAL_DIR := $(GET_LOCAL_DIR)
LOCAL_PATH := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TARGET_ARCH := $(ARCH)
TARGET_2ND_ARCH := $(ARCH)

# Reset local variables
LOCAL_CFLAGS :=
LOCAL_C_INCLUDES :=
LOCAL_SRC_FILES :=
LOCAL_SRC_FILES_$(TARGET_ARCH) :=
LOCAL_SRC_FILES_$(TARGET_2ND_ARCH) :=
LOCAL_CFLAGS_$(TARGET_ARCH) :=
LOCAL_CFLAGS_$(TARGET_2ND_ARCH) :=
LOCAL_ADDITIONAL_DEPENDENCIES :=

# Tongsuo-mini specific configuration
MODULE_CFLAGS += -DTSM_HAVE_SM3
MODULE_CFLAGS += -DTSM_HAVE_SM4
MODULE_CFLAGS += -DTSM_HAVE_ASCON
MODULE_CFLAGS += -DTSM_LOG
MODULE_CFLAGS += -DTSM_ERRSTR
MODULE_CFLAGS += -DNDEBUG

# Disable features not suitable for TEE environment
MODULE_CFLAGS += -D__linux__ -D__TRUSTY__

# Core tongsuo-mini source files (always included)
TONGSUO_MINI_CORE_SRCS := \
	src/error.c \
	src/mem.c \
	src/meth.c \
	src/pool.c \
	src/version.c \
	src/log.c

# Cryptographic algorithm source files
TONGSUO_MINI_CRYPTO_SRCS := \
	src/sm3.c \
	src/sm4.c \
	src/ascon.c \
	src/hmac.c

# Optional protocol source files (OSCORE)
TONGSUO_MINI_OSCORE_SRCS := \
	src/oscore.c \
	src/oscore_cbor.c \
	src/oscore_context.c \
	src/oscore_cose.c \
	src/oscore_crypto.c

# Optional ASN.1 support
TONGSUO_MINI_ASN1_SRCS := \
	src/asn1.c

# Conditionally include sources based on build configuration
# For TEE environment, we include core crypto algorithms by default
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_MINI_CORE_SRCS))
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_MINI_CRYPTO_SRCS))

# Include ASN.1 support if needed
ifeq ($(TONGSUO_MINI_WITH_ASN1),true)
MODULE_CFLAGS += -DTSM_HAVE_ASN1
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_MINI_ASN1_SRCS))
endif

# Include OSCORE support if needed
ifeq ($(TONGSUO_MINI_WITH_OSCORE),true)
MODULE_CFLAGS += -DTSM_HAVE_OSCORE
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(TONGSUO_MINI_OSCORE_SRCS))
endif

# Set include directories
MODULE_INCLUDES += $(LOCAL_DIR)/include/internal

# Export public include directories
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# Set library name if not already set
LIB_NAME ?= tongsuo-mini

# Include the library build system
include make/rctee_lib.mk 