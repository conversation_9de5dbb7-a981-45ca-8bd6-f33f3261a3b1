# Tongsuo-mini 算法移植架构设计

## 1. 项目概述

### 1.1 目标
将Tongsuo完整库中的密码学算法移植到tongsuomini，支持以下算法类别：

- **对称加密**: DES, 3DES, AES (ECB/CBC/CTR/CCM/GCM/XTS - 128/192/256位)
- **消息摘要**: MD5, SHA-1, SHA-224/256/384/512, SHA3-224/256/384/512, SHAKE128/256
- **MAC**: DES-MAC, AES-MAC, AES-CMAC, HMAC (基于所有摘要算法)
- **认证加密**: AES-CCM, AES-GCM (支持AAD)
- **非对称加密**: RSA, DSA, ECDSA/ECDH, EdDSA, X25519/X448
- **密钥派生**: DH_SHARED_SECRET, ECDH_SHARED_SECRET, HKDF
- **国密算法**: SM2 (公钥加密/数字签名/密钥交换), SM3, SM4
- **随机数生成**: 安全随机数生成器
- **大素数API**: 大素数生成和验证

### 1.2 设计原则
- **模块化**: 每个算法类别独立模块，支持按需编译
- **统一接口**: 遵循现有tongsuomini的接口设计模式
- **最小化依赖**: 避免引入不必要的外部依赖
- **TEE优化**: 针对TEE环境的内存和性能优化
- **向后兼容**: 保持现有API的兼容性

## 2. 现有架构分析

### 2.1 当前实现的算法
- SM3: 国密哈希算法
- SM4: 国密分组密码 (仅支持CBC模式)
- ASCON: 轻量级AEAD算法
- HMAC: 基于SM3和ASCON的HMAC

### 2.2 架构特点
- **方法注册机制**: 使用TSM_HASH_METH结构体统一管理哈希算法
- **上下文管理**: 每个算法有独立的上下文结构
- **错误处理**: 统一的错误码定义和错误字符串转换
- **内存管理**: 统一的内存分配和释放接口

## 3. 扩展架构设计

### 3.1 算法分类和标识符扩展

```c
/* 扩展哈希算法标识符 */
enum {
    TSM_HASH_SM3 = 1,
    TSM_HASH_ASCON_HASH,
    TSM_HASH_ASCON_HASHA,
    TSM_HASH_MD5,
    TSM_HASH_SHA1,
    TSM_HASH_SHA224,
    TSM_HASH_SHA256,
    TSM_HASH_SHA384,
    TSM_HASH_SHA512,
    TSM_HASH_SHA3_224,
    TSM_HASH_SHA3_256,
    TSM_HASH_SHA3_384,
    TSM_HASH_SHA3_512,
    TSM_HASH_SHAKE128,
    TSM_HASH_SHAKE256,
};

/* 对称加密算法标识符 */
enum {
    TSM_CIPHER_DES = 1,
    TSM_CIPHER_3DES_EDE2,
    TSM_CIPHER_3DES_EDE3,
    TSM_CIPHER_AES_128,
    TSM_CIPHER_AES_192,
    TSM_CIPHER_AES_256,
    TSM_CIPHER_SM4,
};

/* 非对称算法标识符 */
enum {
    TSM_PKEY_RSA = 1,
    TSM_PKEY_DSA,
    TSM_PKEY_EC,
    TSM_PKEY_ED25519,
    TSM_PKEY_ED448,
    TSM_PKEY_X25519,
    TSM_PKEY_X448,
    TSM_PKEY_SM2,
};
```

### 3.2 方法注册机制扩展

```c
/* 对称加密方法结构 */
typedef struct {
    const char *name;
    uint8_t alg;
    uint8_t key_len;
    uint8_t block_size;
    uint8_t iv_len;
    void *(*newctx)(void);
    void (*freectx)(void *ctx);
    int (*init)(void *ctx, int mode, const unsigned char *key, 
                const unsigned char *iv, int flags);
    int (*update)(void *ctx, const unsigned char *in, size_t inl,
                  unsigned char *out, size_t *outl);
    int (*final)(void *ctx, unsigned char *out, size_t *outl);
} TSM_CIPHER_METH;

/* 非对称算法方法结构 */
typedef struct {
    const char *name;
    uint8_t alg;
    void *(*newctx)(void);
    void (*freectx)(void *ctx);
    int (*keygen)(void *ctx, int bits);
    int (*sign)(void *ctx, const unsigned char *tbs, size_t tbslen,
                unsigned char *sig, size_t *siglen);
    int (*verify)(void *ctx, const unsigned char *tbs, size_t tbslen,
                  const unsigned char *sig, size_t siglen);
    int (*encrypt)(void *ctx, const unsigned char *in, size_t inlen,
                   unsigned char *out, size_t *outlen);
    int (*decrypt)(void *ctx, const unsigned char *in, size_t inlen,
                   unsigned char *out, size_t *outlen);
} TSM_PKEY_METH;
```

### 3.3 目录结构设计

```
opensource_libs/tongsuo-mini/
├── include/
│   ├── tongsuo/
│   │   ├── minisuo.h          # 核心定义和错误码
│   │   ├── hash.h             # 统一哈希接口
│   │   ├── cipher.h           # 统一对称加密接口
│   │   ├── pkey.h             # 统一非对称算法接口
│   │   ├── mac.h              # MAC算法接口
│   │   ├── kdf.h              # 密钥派生接口
│   │   ├── rand.h             # 随机数接口
│   │   ├── bn.h               # 大数运算接口
│   │   ├── md5.h              # MD5算法
│   │   ├── sha.h              # SHA系列算法
│   │   ├── sha3.h             # SHA3系列算法
│   │   ├── des.h              # DES/3DES算法
│   │   ├── aes.h              # AES算法
│   │   ├── rsa.h              # RSA算法
│   │   ├── dsa.h              # DSA算法
│   │   ├── ec.h               # 椭圆曲线算法
│   │   ├── sm2.h              # SM2算法
│   │   ├── sm3.h              # SM3算法 (已存在)
│   │   └── sm4.h              # SM4算法 (已存在)
│   └── internal/
│       ├── meth.h             # 方法注册 (扩展)
│       ├── hash_local.h       # 哈希算法内部定义
│       ├── cipher_local.h     # 对称加密内部定义
│       ├── pkey_local.h       # 非对称算法内部定义
│       └── ...
├── src/
│   ├── hash/
│   │   ├── md5.c
│   │   ├── sha1.c
│   │   ├── sha256.c
│   │   ├── sha512.c
│   │   ├── sha3.c
│   │   └── hash_meth.c
│   ├── cipher/
│   │   ├── des.c
│   │   ├── aes.c
│   │   ├── aes_modes.c
│   │   └── cipher_meth.c
│   ├── pkey/
│   │   ├── rsa.c
│   │   ├── dsa.c
│   │   ├── ec.c
│   │   ├── sm2.c
│   │   └── pkey_meth.c
│   ├── mac/
│   │   ├── cmac.c
│   │   └── mac_meth.c
│   ├── kdf/
│   │   ├── hkdf.c
│   │   └── kdf_meth.c
│   ├── rand/
│   │   └── rand.c
│   ├── bn/
│   │   ├── bn_lib.c
│   │   ├── bn_prime.c
│   │   └── bn_math.c
│   └── core/
│       ├── error.c            # 错误处理 (已存在)
│       ├── mem.c              # 内存管理 (已存在)
│       ├── meth.c             # 方法注册 (扩展)
│       └── version.c          # 版本信息 (已存在)
```

## 4. 接口设计规范

### 4.1 命名规范
- 所有公开API以`tsm_`前缀开头
- 算法特定API以`tsm_<algorithm>_`开头
- 内部函数以`tsm_internal_`开头
- 常量以`TSM_`开头

### 4.2 错误处理
- 所有API返回int类型错误码
- 成功返回TSM_OK (0)
- 失败返回具体错误码 (负数)
- 扩展错误码定义以支持新算法

### 4.3 内存管理
- 统一使用tsm_malloc/tsm_free
- 上下文结构体通过newctx/freectx管理
- 敏感数据使用tsm_memzero清零

## 5. 编译配置设计

### 5.1 模块化编译选项
```makefile
# 哈希算法模块
TONGSUO_MINI_WITH_MD5 ?= true
TONGSUO_MINI_WITH_SHA1 ?= true
TONGSUO_MINI_WITH_SHA256 ?= true
TONGSUO_MINI_WITH_SHA512 ?= true
TONGSUO_MINI_WITH_SHA3 ?= true

# 对称加密模块
TONGSUO_MINI_WITH_DES ?= true
TONGSUO_MINI_WITH_AES ?= true

# 非对称算法模块
TONGSUO_MINI_WITH_RSA ?= true
TONGSUO_MINI_WITH_DSA ?= true
TONGSUO_MINI_WITH_EC ?= true
TONGSUO_MINI_WITH_SM2 ?= true

# 其他模块
TONGSUO_MINI_WITH_MAC ?= true
TONGSUO_MINI_WITH_KDF ?= true
TONGSUO_MINI_WITH_RAND ?= true
TONGSUO_MINI_WITH_BN ?= true
```

### 5.2 条件编译
使用预处理器宏控制算法的包含：
```c
#ifdef TSM_HAVE_MD5
// MD5相关代码
#endif

#ifdef TSM_HAVE_AES
// AES相关代码
#endif
```

## 6. 测试策略

### 6.1 单元测试
- 每个算法独立的测试文件
- 标准测试向量验证
- 边界条件测试
- 错误处理测试

### 6.2 集成测试
- 算法组合使用测试
- 性能基准测试
- 内存泄漏检测
- 多线程安全测试

### 6.3 兼容性测试
- 与标准实现的兼容性验证
- 不同平台的兼容性测试
- TEE环境特定测试

## 7. 实施计划

按照以下顺序逐步实施：
1. 扩展核心架构和接口定义
2. 实现消息摘要算法 (MD5, SHA系列)
3. 实现对称加密算法 (DES, AES)
4. 实现MAC算法
5. 实现认证加密算法
6. 实现非对称算法
7. 实现密钥派生和随机数生成
8. 实现国密算法扩展
9. 实现大素数API
10. 完善构建系统和测试

每个阶段完成后进行充分测试，确保质量和稳定性。
