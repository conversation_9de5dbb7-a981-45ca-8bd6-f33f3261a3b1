# Tongsuo-mini 集成指南

本文档描述了如何在 Trusty TEE 项目中集成和使用 tongsuo-mini 轻量级密码学库。

## 概述

Tongsuo-mini 是一个专为嵌入式系统和物联网设备设计的轻量级密码学算法库，特别适合在 TEE（可信执行环境）等资源受限的环境中使用。

## 集成状态

✅ **已完成的集成**：
- [x] 创建了用户空间库包装器 (`user/base/lib/tongsuo-mini/`)
- [x] 配置了适合 TEE 环境的默认算法模块
- [x] 创建了完整的示例应用 (`user/app/sample/tongsuo-mini-demo/`)
- [x] 提供了详细的使用文档和 API 示例

## 支持的算法

### 默认启用
- **SM3**: 国密哈希算法 (256-bit)
- **SM4**: 国密分组密码算法 (128-bit)
- **ASCON**: 轻量级 AEAD 认证加密算法
- **HMAC**: 基于哈希的消息认证码

### 可选模块（需要时可启用）
- **ASN.1**: ASN.1 编解码支持
- **OSCORE**: CoAP 对象安全协议

## 文件结构

```
trusty-tee/
├── opensource_libs/tongsuo-mini/           # 上游 tongsuo-mini 源码
│   ├── rules.mk                           # TEE 构建系统集成
│   ├── include/tongsuo/                   # 公开 API 头文件
│   └── src/                               # 算法实现源码
├── user/base/lib/tongsuo-mini/            # 用户空间库包装器
│   ├── rules.mk                           # 库构建配置
│   └── README.md                          # 使用说明
└── user/app/sample/tongsuo-mini-demo/     # 示例应用
    ├── manifest.json                      # 应用清单
    ├── rules.mk                           # 应用构建配置
    └── tongsuo_mini_demo.c                # 示例代码
```

## 快速开始

### 1. 在应用中使用 tongsuo-mini

在您的应用的 `rules.mk` 中添加依赖：

```makefile
MODULE_DEPS += \
    user/base/lib/tongsuo-mini \
    user/base/lib/libc-rctee
```

### 2. 包含必要的头文件

```c
#include <tongsuo/minisuo.h>  // 基础定义和错误码
#include <tongsuo/sm3.h>      // SM3 哈希算法
#include <tongsuo/sm4.h>      // SM4 分组密码
#include <tongsuo/ascon.h>    // ASCON AEAD 算法
```

### 3. 运行示例

示例应用位于 `user/app/sample/tongsuo-mini-demo/`，演示了：

- SM3 哈希计算
- SM4 ECB 模式加解密
- ASCON AEAD 认证加密

## 构建配置

可以通过设置环境变量来控制编译的算法模块：

```makefile
# 在 rules.mk 中自定义配置
TONGSUO_MINI_WITH_SM3 := true      # 启用 SM3
TONGSUO_MINI_WITH_SM4 := true      # 启用 SM4
TONGSUO_MINI_WITH_ASCON := true    # 启用 ASCON
TONGSUO_MINI_WITH_HMAC := true     # 启用 HMAC
TONGSUO_MINI_WITH_ASN1 := false    # 禁用 ASN.1
TONGSUO_MINI_WITH_OSCORE := false  # 禁用 OSCORE
```

## 性能特点

- **内存占用小**: 核心算法实现占用 <20KB
- **模块化设计**: 只编译需要的算法，避免代码膨胀
- **TEE 优化**: 适配 Trusty TEE 的内存管理和安全要求
- **国密支持**: 原生支持中国商用密码标准

## 扩展规划

### 短期扩展（推荐优先实现）
- **SM2**: 国密椭圆曲线公钥密码算法
- **EdDSA**: Edwards 曲线数字签名算法
- **AES**: 高级加密标准（如果需要国际标准兼容）

### 长期扩展
- **RSA**: RSA 公钥密码算法
- **ECDSA**: 椭圆曲线数字签名算法
- **SHA-2/SHA-3**: 其他哈希算法
- **ChaCha20-Poly1305**: 现代流密码 + AEAD

## 开发建议

### 1. 算法选择
- **对称加密**: 优先使用 SM4，如需国际兼容可考虑 AES
- **哈希算法**: 优先使用 SM3，轻量级场景可考虑 ASCON-Hash
- **认证加密**: 使用 ASCON AEAD，适合低功耗场景
- **消息认证**: 使用 HMAC-SM3 或 HMAC-ASCON

### 2. 性能优化
- 使用 oneshot API 减少上下文开销
- 合理设置缓冲区大小避免多次调用
- 在循环中重用上下文对象

### 3. 安全注意事项
- 密钥和随机数使用 TEE 的安全随机数生成器
- 敏感数据使用后及时清零
- 合理设置 IV/nonce，避免重复使用

## 许可证

Tongsuo-mini 遵循 Apache 2.0 许可证，与 Trusty TEE 项目兼容。

## 参考资源

- [Tongsuo-mini 官方文档](https://tongsuo.net/docs/minisuo/)
- [Tongsuo-mini GitHub 仓库](https://github.com/Tongsuo-Project/tongsuo-mini)
- [国密算法标准文档](http://www.oscca.gov.cn/)
- [ASCON 轻量级密码学](https://ascon.iaik.tugraz.at/)

## 更新日志

- **2024-12-19**: 完成基础集成，支持 SM3/SM4/ASCON/HMAC
- **计划**: 添加 SM2 椭圆曲线算法支持 